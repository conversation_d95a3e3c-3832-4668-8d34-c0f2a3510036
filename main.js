import '/style.css';
import * as THREE from 'three';
//import { OrbitControls } from 'three/examples/jsm/controls/OrbitControls';

// Setup
var scene, camera, renderer, clock, binormal, normal, tube;

init();

function init(){

 let ww = window.innerWidth
 let wh = window.innerHeight

 clock = new THREE.Clock();

 scene = new THREE.Scene()

 camera = new THREE.PerspectiveCamera(35,  ww/ wh, 0.1, 1000)
 camera.position.set(0, 4, 57);//wide position
 camera.lookAt(0,1.5,0);

 const ambient = new THREE.HemisphereLight(0xffffbb, 0x080820);
  scene.add(ambient);
  
  const light = new THREE.DirectionalLight(0xFFFFFF, 1);
  light.position.set( 1, 10, 6);
  scene.add(light);

  //const container = document.getElementById( 'hero' );

  // const renderer = new THREE.WebGLRenderer({
  //   canvas: document.querySelector('#hero'),
  // });
 
 renderer = new THREE.WebGLRenderer({ antialias: true })
 renderer.setSize( ww,  wh)
 //container.appendChild( renderer.domElement)

 document.body.appendChild( renderer.domElement)

 

 //renderer.render(scene, camera)

//Tube avec class

class CustomSinCurve extends THREE.Curve {

	constructor( scale = 1 ) {

		super();

		 this.scale = scale;

	}

	getPoint( t, optionalTarget = new THREE.Vector3() ) {

		const tx = Math.cos( 2 * Math.PI * t );
		const ty = Math.sin( 2 * Math.PI * t );
		const tz = 0.2 * Math.sin(8 * Math.PI * t)

		return optionalTarget.set( tx, ty, tz ).multiplyScalar(  this.scale );

	}

}



// const path = new CustomSinCurve( 10 );
// const geometry = new THREE.TubeGeometry( path, 20, 2, 8, false );
// const material = new THREE.MeshBasicMaterial( { color: 0x00ff00 } );



	    const path = new CustomSinCurve(50)

      const geometry = new THREE.TubeGeometry(path, 64, 2, 8, false)

      const material = new THREE.MeshBasicMaterial({ 
        // color: 0xffffff,
        // wireframe: true,
        side: THREE.DoubleSide,
        map: new THREE.TextureLoader().load('img/back10.jpg')
      })

      tube = new THREE.Mesh( geometry, material );
scene.add( tube );

	 

//Tube from example file

// const curve = new THREE.Curves.GrannyKnot()
// const geometry = new THREE.TubeBufferGeometry(curve, 100, 2, 8, true)
// const material = new THREE.MeshBasicMaterial({ 
//         wireframe: true,
//         color: 0xffffff,
        
//         side: THREE.DoubleSide,
//         //map: new THREE.TextureLoader().load('img/back4c.jpg')
//       })

//       tube = new THREE.Mesh(geometry, material)
//       scene.add(tube)


       //the material is repeatedly extended in the direction of the curve
      //  material.map.wrapS = THREE.MirroredRepeatWrapping;
      //  material.map.wrapT= THREE.MirroredRepeatWrapping;
      //  material.map.repeat.set(2, 1)

  //     material.map.wrapS = THREE.MirroredRepeatWrapping;
  // material.map.wrapT = THREE.MirroredRepeatWrapping;
  // material.map.repeat.set(
  //   material.repx,
  //   material.repy
  // )

// Ajouter des objets start

let planes = []
      const tex1 = new THREE.TextureLoader().load('img/html5.svg')
      //const html = new THREE.Mesh(new THREE.BoxGeometry(3, 3, 3), new THREE.MeshBasicMaterial({ map: tex1 }))

      const tex2 = new THREE.TextureLoader().load('img/css3.svg')
      //const css = new THREE.Mesh(new THREE.BoxGeometry(3, 3, 3), new THREE.MeshBasicMaterial({map: tex2 }))

      const tex3 = new THREE.TextureLoader().load('img/python.svg')

      const tex4 = new THREE.TextureLoader().load('img/wordpress.svg')

      const tex5 = new THREE.TextureLoader().load('img/javascript.svg')

      const tex6 = new THREE.TextureLoader().load('img/vue.svg')

      const tex7 = new THREE.TextureLoader().load('img/nuxt.svg')

      const tex8 = new THREE.TextureLoader().load('img/bootstrap.svg')

      const tex9 = new THREE.TextureLoader().load('img/graphql.svg')

      const tex10 = new THREE.TextureLoader().load('img/json.svg')

      
      //new THREE.SphereGeometry(1, 4, 4)
      const geo =  new THREE.BoxGeometry(0.25, 0.25, 0.25)

      const mat1 = new THREE.MeshBasicMaterial({
        map: tex1,
        //color: 0x00ff00
      })

      const mat2 = new THREE.MeshBasicMaterial({
        map: tex2,
        //color: 0xffffff
      })

      const mat3 = new THREE.MeshBasicMaterial({
        map: tex3
      })

      const mat4 = new THREE.MeshBasicMaterial({
        map: tex4
      })

      const mat5 = new THREE.MeshBasicMaterial({
        map: tex5
      })

      const mat6 = new THREE.MeshBasicMaterial({
        map: tex6
      })

      const mat7 = new THREE.MeshBasicMaterial({
        map: tex7
      })

      const mat8 = new THREE.MeshBasicMaterial({
        map: tex8
      })

      const mat9 = new THREE.MeshBasicMaterial({
        map: tex9
      })

      const mat10 = new THREE.MeshBasicMaterial({
        map: tex10
      })

      geo.center()


      const mesh2 = new THREE.Mesh(geo, mat1)

      
      

      // mesh2.position.copy(geometry.parameters.path.getPointAt(0.04))

      // scene.add(mesh2)

      

      // function random_item(items)
      //   {
          
      //   return items[Math.floor(Math.random()*items.length)];
            
      //   }

      const items = [mat1, mat2, mat3, mat4, mat5,mat6, mat7, mat8,mat9, mat10,mat1, mat2, mat3, mat4, mat5,mat6, mat7, mat8,mat9, mat10,mat1, mat2, mat3, mat4, mat5,mat6, mat7, mat8,mat9, mat10]

      for(let i = 0; i < 30; i++) {
        const plane = mesh2.clone()
        
        // if(i % 2 == 0) {
        //   plane.material = mat1
        // } else {
        //   plane.material = mat2
        // }

       plane.material = items[i]

        // plane.material = mat1

        //plane.material = mat10

        //console.log(random_item(items))
        

        plane.position.copy(geometry.parameters.path.getPointAt(i * 0.04))
        
        planes.push(plane)
        scene.add(plane)

      
      }

  

      // scene.add(html)
      // scene.add(css)
// Ajouter des objets end

      binormal = new THREE.Vector3();
      normal = new THREE.Vector3();

      window.addEventListener( 'resize', resize, false)
      update()
    }


// Move the camera along the tube

function updateCamera(){

//let clock = new THREE.Clock()
const time =  clock.getElapsedTime()
const looptime = 75
const t = (time % looptime)/ looptime
const t2 = ((time + 0.1) % looptime)/ looptime

const pos = tube.geometry.parameters.path.getPointAt(t)
const pos2 = tube.geometry.parameters.path.getPointAt(t2)



camera.position.copy(pos)
camera.lookAt(pos2)
	  
}

// Ajouter des objets

// let planes = []
//       const tex1 = new THREE.TextureLoader().load('img/t1.png')
//       const tex2 = new THREE.TextureLoader().load('img/t2.png')
//       const geo = new THREE.PlaneBufferGeometry(0.5, 0.5)
//       const mat1 = new THREE.MeshBasicMaterial({
//         transparent: true,
//         map: tex1
//       })
//       const mat2 = new THREE.MeshBasicMaterial({
//         transparent: true,
//         map: tex2
//       })
//       geo.center()
//       const mesh2 = new THREE.Mesh(geo, mat1)
//       for(let i = 0; i < 20; i++) {
//         const plane = mesh2.clone()
//         if(i % 2 == 0) {
//           plane.material = mat1
//         } else {
//           plane.material = mat2
//         }
//         plane.position.copy(this.tubeGeometry.parameters.path.getPointAt(i * 0.04))
//         planes.push(plane)
//         scene.add(plane)
//       }






// animate()

// function animate() {
// 	requestAnimationFrame( animate );
//   updateCamera()
// 	renderer.render( scene, camera );
  

// 	// mesh.rotation.x += 0.01;
// 	// mesh.rotation.y += 0.01;
// }

function update(){
  requestAnimationFrame( update );
  updateCamera();

  // mesh2.rotation.x += 0.05;
  // mesh2.rotation.y += 0.075;
  // mesh2.rotation.z += 0.05;


	renderer.render( scene, camera );  
  
}

function resize(){
  camera.aspect = window.innerWidth / window.innerHeight;
  camera.updateProjectionMatrix();
  renderer.setSize( window.innerWidth, window.innerHeight );
}

// end Threejs

//Navbar color

// window.addEventListener('scroll', function(){
//   var navScroll = document.getElementById('navTop');
//   var logoTop = document.getElementById('svg-logo')
//   var navLink = document.querySelector('.nav-links');
//   var y = window.scrollY;
//   if(y > 100){
//     navScroll.style.backgroundColor = 'var(--dark-background-menu)';
//     navScroll.classList.add('shdowBottom');
//     logoTop.classList.remove('svg-logo');
// }else{
//   navScroll.style.backgroundColor = 'transparent';
//   navScroll.classList.remove('shdowBottom');
//   logoTop.classList.add('svg-logo');
  
// }

// })

// Progress Bar

var progressbar = document.getElementById("progressbar");
var totalHeight = document.body.scrollHeight - window.innerHeight;
window.onscroll = function(){
  var progress = (window.pageYOffset / totalHeight) * 100;
  progressbar.style.height = progress + "%";
}

//Dark mode

var dl = document.getElementById('switch');

var darkIcon = document.getElementById('switch-dark');
var lightIcon = document.getElementById('switch-light');

darkIcon.addEventListener('click',()=>{

    document.documentElement.style.setProperty('--dark-background', 'white')
    document.documentElement.style.setProperty('--dark-text', 'black')
    document.documentElement.style.setProperty('--dark-background-menu', '#009ffc')
    darkIcon.style.display = 'none';
    lightIcon.style.display= 'block';

})

lightIcon.addEventListener('click', ()=>{

  document.documentElement.style.setProperty('--dark-background', '#15202B')
    document.documentElement.style.setProperty('--dark-background-menu', '#121c25')
    document.documentElement.style.setProperty('--dark-text', 'white')
    darkIcon.style.display = 'block';
    lightIcon.style.display= 'none';

})

// contact form

var forme = document.getElementById('contact-form');
var conf = document.getElementById('confirmation');
var erreur = document.getElementById('erreur');

forme.addEventListener('submit', function(e){
  e.preventDefault();

  const formData = new FormData(this);

  fetch('contact.php',{
    method:'post',
    body: formData
  }).then(function(response){
    return response.text();
  }).then(function(text){
    console.log(text);
    conf.style.display = 'block';
  }).catch(function(error){
    console.error(error)
    erreur.style.display = 'block';
  })
  
})


// menu responsive

var menuicon= document.getElementById('menuicon');
var x = document.getElementById("nav-links");
var navTop = document.getElementById("navTop");
var menuOpen = document.getElementById("menuOpen");
var menuClose = document.getElementById("menuClose");

function cacherMenu(){
  if (menuClose.style.display === 'block'){
  x.className = "nav-links";
  navTop.style.position = 'fixed';
  menuOpen.style.display='block';
  menuClose.style.display='none';
  navTop.style.backgroundColor='transparent';
}
}

menuicon.addEventListener('click', function(){

  if (menuOpen.style.display === "block") {
    x.className += " menuSmall";
    
    navTop.style.position = 'fixed';
    menuOpen.style.display='none';
    menuClose.style.display='block';
    navTop.style.backgroundColor='var(--dark-background-menu)';
  } else {
    cacherMenu();
  }

})

// cacher le menu
var menus = document.querySelectorAll('.nav-link');

menus.forEach((menu) => {
  menu.addEventListener('click', function(){
    cacherMenu();
  });
  
});






