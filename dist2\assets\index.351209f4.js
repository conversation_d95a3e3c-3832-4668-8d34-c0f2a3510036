import{S as e,P as t,H as n,D as o,W as s,T as a,M as d,a as i,b as l,c,B as r,C as m,V as p,d as u}from"./vendor.c01d4316.js";var y,w,g,h,k;function v(){requestAnimationFrame(v),function(){const e=h.getElapsedTime(),t=e%75/75,n=(e+.1)%75/75,o=k.geometry.parameters.path.getPointAt(t),s=k.geometry.parameters.path.getPointAt(n);w.position.copy(o),w.lookAt(s)}(),g.render(y,w)}function E(){w.aspect=window.innerWidth/window.innerHeight,w.updateProjectionMatrix(),g.setSize(window.innerWidth,window.innerHeight)}!function(){let f=window.innerWidth,b=window.innerHeight;h=new u,y=new e,(w=new t(35,f/b,.1,1e3)).position.set(0,4,57),w.lookAt(0,1.5,0);const I=new n(16777147,526368);y.add(I);const P=new o(16777215,1);P.position.set(1,10,6),y.add(P),(g=new s({antialias:!0})).setSize(f,b),document.body.appendChild(g.domElement);const B=new class extends m{constructor(e=1){super(),this.scale=e}getPoint(e,t=new p){const n=Math.cos(2*Math.PI*e),o=Math.sin(2*Math.PI*e),s=.2*Math.sin(8*Math.PI*e);return t.set(n,o,s).multiplyScalar(this.scale)}}(50),x=new a(B,200,2,8,!1),M=new d({side:i,map:(new l).load("img/back4f.jpg")});k=new c(x,M),y.add(k);const A=(new l).load("img/html5.svg"),H=(new l).load("img/css3.svg"),L=(new l).load("img/python.svg"),S=(new l).load("img/wordpress.svg"),j=(new l).load("img/javascript.svg"),C=(new l).load("img/vue.svg"),W=(new l).load("img/nuxt.svg"),q=(new l).load("img/bootstrap.svg"),z=(new l).load("img/graphql.svg"),D=(new l).load("img/json.svg"),T=new r(.25,.25,.25),F=new d({map:A}),N=new d({map:H}),O=new d({map:L}),V=new d({map:S}),Y=new d({map:j}),G=new d({map:C}),J=new d({map:W}),K=new d({map:q}),Q=new d({map:z}),R=new d({map:D});T.center();const U=new c(T,F),X=[F,N,O,V,Y,G,J,K,Q,R,F,N,O,V,Y,G,J,K,Q,R,F,N,O,V,Y,G,J,K,Q,R,F,N,O,V,Y,G,J,K,Q,R];for(let e=0;e<30;e++){const t=U.clone();t.material=X[e],t.position.copy(x.parameters.path.getPointAt(.04*e)),y.add(t)}window.addEventListener("resize",E,!1),v()}();var f=document.getElementById("progressbar"),b=document.body.scrollHeight-window.innerHeight;window.onscroll=function(){var e=window.pageYOffset/b*100;f.style.height=e+"%"},document.getElementById("switch");var I=document.getElementById("switch-dark"),P=document.getElementById("switch-light");I.addEventListener("click",(()=>{document.documentElement.style.setProperty("--dark-background","white"),document.documentElement.style.setProperty("--dark-text","black"),document.documentElement.style.setProperty("--dark-background-menu","#009ffc"),I.style.display="none",P.style.display="block"})),P.addEventListener("click",(()=>{document.documentElement.style.setProperty("--dark-background","#15202B"),document.documentElement.style.setProperty("--dark-background-menu","#121c25"),document.documentElement.style.setProperty("--dark-text","white"),I.style.display="block",P.style.display="none"}));var B=document.getElementById("contact-form"),x=document.getElementById("confirmation"),M=document.getElementById("erreur");B.addEventListener("submit",(function(e){e.preventDefault();const t=new FormData(this);fetch("contact.php",{method:"post",body:t}).then((function(e){return e.text()})).then((function(e){console.log(e),x.style.display="block"})).catch((function(e){console.error(e),M.style.display="block"}))}));var A=document.getElementById("menuicon"),H=document.getElementById("nav-links"),L=document.getElementById("navTop"),S=document.getElementById("menuOpen"),j=document.getElementById("menuClose");function C(){"block"===j.style.display&&(H.className="nav-links",L.style.position="fixed",S.style.display="block",j.style.display="none",L.style.backgroundColor="transparent")}A.addEventListener("click",(function(){"block"===S.style.display?(H.className+=" menuSmall",L.style.position="fixed",S.style.display="none",j.style.display="block",L.style.backgroundColor="var(--dark-background-menu)"):C()})),document.querySelectorAll(".nav-link").forEach((e=>{e.addEventListener("click",(function(){C()}))}));
